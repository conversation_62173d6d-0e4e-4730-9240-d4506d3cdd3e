{"v": "5.7.4", "fr": 60, "ip": 0, "op": 680, "w": 1202, "h": 1202, "nm": "Masthead Pattern Animation", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Background", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 0, "k": [100, 100, 100]}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [1202, 1202]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 0}}, {"ty": "fl", "c": {"a": 0, "k": [0.05, 0.05, 0.1, 1]}, "o": {"a": 0, "k": 100}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Floating Particle 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 650, "s": [80]}, {"t": 680, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 680, "s": [360]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [200, 200, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 340, "s": [1000, 400, 0]}, {"t": 680, "s": [200, 1000, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [50, 50, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [120, 120, 100]}, {"t": 680, "s": [50, 50, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [40, 40]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.2, 0.6, 1, 1]}, "o": {"a": 0, "k": 100}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Floating Particle 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 620, "s": [60]}, {"t": 650, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 680, "s": [-270]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [1000, 300, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 370, "s": [300, 800, 0]}, {"t": 680, "s": [900, 200, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 370, "s": [150, 150, 100]}, {"t": 680, "s": [80, 80, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [30, 30]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.4, 0.8, 1]}, "o": {"a": 0, "k": 100}}], "ip": 60, "op": 680, "st": 60}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Geometric Shape 1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 150, "s": [40]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 580, "s": [40]}, {"t": 610, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [0]}, {"t": 680, "s": [180]}]}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 120, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 400, "s": [200, 200, 100]}, {"t": 680, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [200, 200]}, "p": {"a": 0, "k": [0, 0]}, "r": {"a": 0, "k": 20}}, {"ty": "st", "c": {"a": 0, "k": [0.8, 0.9, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 3}}], "ip": 120, "op": 680, "st": 120}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Floating Particle 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 210, "s": [70]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 600, "s": [70]}, {"t": 630, "s": [0]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 180, "s": [0]}, {"t": 680, "s": [450]}]}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 180, "s": [600, 100, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 430, "s": [100, 600, 0]}, {"t": 680, "s": [1100, 1100, 0]}]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 180, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 430, "s": [130, 130, 100]}, {"t": 680, "s": [60, 60, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [25, 25]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 1, 0.6, 1]}, "o": {"a": 0, "k": 100}}], "ip": 180, "op": 680, "st": 180}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Wave Pattern", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [20]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 340, "s": [60]}, {"t": 680, "s": [20]}]}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 680, "s": [720]}]}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [150, 150, 100]}, {"t": 680, "s": [100, 100, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [400, 400]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "st", "c": {"a": 0, "k": [0.6, 0.8, 1, 1]}, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}}], "ip": 0, "op": 680, "st": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Pulsing Core", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 170, "s": [80]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 340, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 510, "s": [80]}, {"t": 680, "s": [30]}]}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [601, 601, 0]}, "a": {"a": 0, "k": [0, 0, 0]}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 170, "s": [120, 120, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 340, "s": [80, 80, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 510, "s": [120, 120, 100]}, {"t": 680, "s": [80, 80, 100]}]}}, "ao": 0, "shapes": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [60, 60]}, "p": {"a": 0, "k": [0, 0]}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1]}, "o": {"a": 0, "k": 100}}], "ip": 0, "op": 680, "st": 0}], "markers": []}